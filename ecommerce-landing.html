<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShopZone - متجرك الإلكتروني المتكامل</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        header.scrolled {
            background: rgba(142, 68, 173, 0.95);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            color: #f39c12;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .cart-icon {
            position: relative;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, rgba(142, 68, 173, 0.9), rgba(155, 89, 182, 0.9)),
                        url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="shopping" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><path d="M10,15 L30,15 L28,25 L12,25 Z" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23shopping)"/></svg>');
            animation: float 25s linear infinite;
        }

        @keyframes float {
            0% { transform: translateX(0px) translateY(0px); }
            50% { transform: translateX(-20px) translateY(-10px); }
            100% { transform: translateX(0px) translateY(0px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            animation: slideInUp 1s ease-out 0.3s both;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.95;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .btn-primary {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px 35px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(231, 76, 60, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 15px 35px;
            text-decoration: none;
            border: 2px solid white;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-secondary:hover {
            background: white;
            color: #8e44ad;
            transform: translateY(-3px);
        }

        /* Products Section */
        .products {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(45deg, #8e44ad, #e74c3c);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
            overflow: hidden;
        }

        .product-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .product-card:hover .product-image::before {
            left: 100%;
        }

        .product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .product-badge.sale {
            background: #27ae60;
        }

        .product-badge.new {
            background: #f39c12;
        }

        .product-content {
            padding: 1.5rem;
        }

        .product-category {
            color: #8e44ad;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .product-title {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 600;
        }

        .product-description {
            color: #7f8c8d;
            margin-bottom: 1rem;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 1rem;
        }

        .stars {
            color: #f39c12;
        }

        .rating-text {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .product-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .price-current {
            font-size: 1.5rem;
            font-weight: 700;
            color: #27ae60;
        }

        .price-old {
            font-size: 1.1rem;
            color: #95a5a6;
            text-decoration: line-through;
        }

        .product-actions {
            display: flex;
            gap: 10px;
        }

        .add-to-cart {
            flex: 1;
            background: linear-gradient(45deg, #8e44ad, #9b59b6);
            color: white;
            padding: 10px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .add-to-cart:hover {
            background: linear-gradient(45deg, #7d3c98, #8e44ad);
            transform: translateY(-2px);
        }

        .wishlist-btn {
            background: #ecf0f1;
            color: #7f8c8d;
            padding: 10px 15px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

        .wishlist-btn:hover {
            background: #e74c3c;
            color: white;
            transform: translateY(-2px);
        }

        /* Categories Section */
        .categories {
            padding: 100px 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
        }

        .category-item {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .category-item:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.15);
        }

        .category-icon {
            font-size: 3rem;
            color: #f39c12;
            margin-bottom: 1rem;
            display: block;
        }

        .category-item h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }

        .category-count {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        /* Footer */
        footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #8e44ad;
        }

        .footer-section p, .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            line-height: 1.8;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #8e44ad;
        }

        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #8e44ad;
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: #9b59b6;
            transform: translateY(-3px);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .section-title {
                font-size: 2rem;
            }

            .product-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <div class="logo">
                <i class="fas fa-shopping-bag"></i>
                ShopZone
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#products">المنتجات</a></li>
                <li><a href="#categories">الفئات</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
                <li>
                    <div class="cart-icon">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">3</span>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <h1>تسوق بذكاء، اشتر بثقة</h1>
                <p>اكتشف أفضل المنتجات بأسعار لا تُقاوم. تجربة تسوق فريدة مع خدمة عملاء متميزة وتوصيل سريع لجميع أنحاء المملكة</p>
                <div class="hero-buttons">
                    <a href="#products" class="btn-primary">
                        <i class="fas fa-shopping-cart"></i>
                        تسوق الآن
                    </a>
                    <a href="#categories" class="btn-secondary">
                        <i class="fas fa-list"></i>
                        تصفح الفئات
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="products" id="products">
        <div class="container">
            <h2 class="section-title">المنتجات الأكثر مبيعاً</h2>
            <p class="section-subtitle">اكتشف أحدث المنتجات وأكثرها شعبية لدى عملائنا</p>

            <div class="products-grid">
                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <div class="product-badge sale">خصم 30%</div>
                    </div>
                    <div class="product-content">
                        <div class="product-category">سماعات</div>
                        <h3 class="product-title">سماعات لاسلكية عالية الجودة</h3>
                        <p class="product-description">سماعات بتقنية إلغاء الضوضاء وجودة صوت استثنائية</p>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">(245 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="price-current">299 ريال</span>
                            <span class="price-old">429 ريال</span>
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart">
                                <i class="fas fa-cart-plus"></i>
                                أضف للسلة
                            </button>
                            <button class="wishlist-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <div class="product-badge new">جديد</div>
                    </div>
                    <div class="product-content">
                        <div class="product-category">ساعات</div>
                        <h3 class="product-title">ساعة ذكية متطورة</h3>
                        <p class="product-description">ساعة ذكية مع مراقبة الصحة وإشعارات ذكية</p>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-text">(189 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="price-current">899 ريال</span>
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart">
                                <i class="fas fa-cart-plus"></i>
                                أضف للسلة
                            </button>
                            <button class="wishlist-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <div class="product-badge">الأكثر مبيعاً</div>
                    </div>
                    <div class="product-content">
                        <div class="product-category">أحذية</div>
                        <h3 class="product-title">حذاء رياضي عصري</h3>
                        <p class="product-description">حذاء رياضي مريح ومناسب لجميع الأنشطة اليومية</p>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="rating-text">(567 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="price-current">449 ريال</span>
                            <span class="price-old">599 ريال</span>
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart">
                                <i class="fas fa-cart-plus"></i>
                                أضف للسلة
                            </button>
                            <button class="wishlist-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image" style="background-image: url('https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <div class="product-badge sale">خصم 25%</div>
                    </div>
                    <div class="product-content">
                        <div class="product-category">حقائب</div>
                        <h3 class="product-title">حقيبة ظهر أنيقة</h3>
                        <p class="product-description">حقيبة ظهر عملية ومقاومة للماء مع تصميم عصري</p>
                        <div class="product-rating">
                            <div class="stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <span class="rating-text">(123 تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="price-current">199 ريال</span>
                            <span class="price-old">265 ريال</span>
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart">
                                <i class="fas fa-cart-plus"></i>
                                أضف للسلة
                            </button>
                            <button class="wishlist-btn">
                                <i class="fas fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="categories" id="categories">
        <div class="container">
            <h2 class="section-title">تسوق حسب الفئة</h2>
            <div class="categories-grid">
                <div class="category-item">
                    <i class="fas fa-mobile-alt category-icon"></i>
                    <h3>إلكترونيات</h3>
                    <p class="category-count">250+ منتج</p>
                </div>
                <div class="category-item">
                    <i class="fas fa-tshirt category-icon"></i>
                    <h3>ملابس</h3>
                    <p class="category-count">180+ منتج</p>
                </div>
                <div class="category-item">
                    <i class="fas fa-home category-icon"></i>
                    <h3>منزل وحديقة</h3>
                    <p class="category-count">320+ منتج</p>
                </div>
                <div class="category-item">
                    <i class="fas fa-dumbbell category-icon"></i>
                    <h3>رياضة ولياقة</h3>
                    <p class="category-count">95+ منتج</p>
                </div>
                <div class="category-item">
                    <i class="fas fa-book category-icon"></i>
                    <h3>كتب وقرطاسية</h3>
                    <p class="category-count">150+ منتج</p>
                </div>
                <div class="category-item">
                    <i class="fas fa-gamepad category-icon"></i>
                    <h3>ألعاب</h3>
                    <p class="category-count">75+ منتج</p>
                </div>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopZone</h3>
                    <p>متجرك الإلكتروني الموثوق للحصول على أفضل المنتجات بأسعار تنافسية وخدمة عملاء متميزة.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <p><a href="#products">المنتجات</a></p>
                    <p><a href="#categories">الفئات</a></p>
                    <p><a href="#">العروض الخاصة</a></p>
                    <p><a href="#">المدونة</a></p>
                </div>
                <div class="footer-section">
                    <h3>خدمة العملاء</h3>
                    <p><a href="#">مركز المساعدة</a></p>
                    <p><a href="#">سياسة الإرجاع</a></p>
                    <p><a href="#">تتبع الطلب</a></p>
                    <p><a href="#">الشحن والتوصيل</a></p>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📧 <EMAIL></p>
                    <p>📞 +966 50 123 4567</p>
                    <p>📍 الرياض، المملكة العربية السعودية</p>
                    <p>🕒 24/7 خدمة العملاء</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 ShopZone. MOSTAFA جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe product cards
        document.querySelectorAll('.product-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `all 0.6s ease ${index * 0.1}s`;
            observer.observe(card);
        });

        // Observe category items
        document.querySelectorAll('.category-item').forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(50px)';
            item.style.transition = `all 0.6s ease ${index * 0.1}s`;
            observer.observe(item);
        });

        // Add to cart functionality
        let cartCount = 3;
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function() {
                cartCount++;
                document.querySelector('.cart-count').textContent = cartCount;

                this.innerHTML = '<i class="fas fa-check"></i> تم الإضافة!';
                this.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-cart-plus"></i> أضف للسلة';
                    this.style.background = 'linear-gradient(45deg, #8e44ad, #9b59b6)';
                }, 2000);
            });
        });

        // Wishlist functionality
        document.querySelectorAll('.wishlist-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.style.color === 'rgb(231, 76, 60)') {
                    this.style.color = '#7f8c8d';
                    this.style.background = '#ecf0f1';
                } else {
                    this.style.color = '#e74c3c';
                    this.style.background = '#fadbd8';
                }
            });
        });

        // Category click animation
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-10px)';
                }, 150);
            });
        });
    </script>
</body>
</html>
