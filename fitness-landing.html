<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FitLife - رحلتك نحو الصحة واللياقة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: rgba(46, 204, 113, 0.95);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        header.scrolled {
            background: rgba(46, 204, 113, 0.98);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            color: #f39c12;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 5px 15px;
            border-radius: 25px;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(rgba(46, 204, 113, 0.8), rgba(39, 174, 96, 0.8)),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><rect fill="%2327ae60" width="1200" height="800"/><g fill-opacity="0.1"><polygon fill="%23fff" points="1200,0 0,0 0,800"/><polygon fill="%23fff" points="1200,800 1200,0 0,800"/></g></svg>');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 15s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.4rem;
            margin-bottom: 2rem;
            animation: slideInUp 1s ease-out 0.3s both;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .btn-primary {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 15px 35px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(243, 156, 18, 0.4);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 15px 35px;
            text-decoration: none;
            border: 2px solid white;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-secondary:hover {
            background: white;
            color: #27ae60;
            transform: translateY(-3px);
        }

        /* Programs Section */
        .programs {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .programs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .program-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .program-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .program-image {
            height: 200px;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .program-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .program-card:hover .program-image::before {
            left: 100%;
        }

        .program-content {
            padding: 2rem;
        }

        .program-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .program-content p {
            color: #7f8c8d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .program-features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .program-features li {
            padding: 5px 0;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .program-features li i {
            color: #27ae60;
        }

        .program-price {
            font-size: 2rem;
            font-weight: 700;
            color: #27ae60;
            text-align: center;
            margin-bottom: 1rem;
        }

        .program-btn {
            width: 100%;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .program-btn:hover {
            background: linear-gradient(45deg, #229954, #27ae60);
            transform: translateY(-2px);
        }

        /* Testimonials */
        .testimonials {
            padding: 100px 0;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .testimonial-card {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .testimonial-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 1rem;
            line-height: 1.6;
        }

        .testimonial-author {
            font-weight: 600;
        }

        /* Footer */
        footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 3rem 0;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #2ecc71;
        }

        .footer-section p, .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            line-height: 1.6;
        }

        .footer-section a:hover {
            color: #2ecc71;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: #27ae60;
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-links a:hover {
            background: #2ecc71;
            transform: translateY(-3px);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <div class="logo">
                <i class="fas fa-dumbbell"></i>
                FitLife
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#programs">البرامج</a></li>
                <li><a href="#testimonials">آراء العملاء</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <h1>ابدأ رحلتك نحو الصحة</h1>
                <p>برامج تدريبية مخصصة وتغذية متوازنة لتحقيق أهدافك في اللياقة البدنية مع أفضل المدربين المعتمدين</p>
                <div class="hero-buttons">
                    <a href="#programs" class="btn-primary">
                        <i class="fas fa-play"></i>
                        ابدأ الآن
                    </a>
                    <a href="#" class="btn-secondary">
                        <i class="fas fa-video"></i>
                        شاهد الفيديو
                    </a>
                </div>
            </div>
        </div>
    </section>

    <section class="programs" id="programs">
        <div class="container">
            <h2 class="section-title">برامجنا التدريبية</h2>
            <p class="section-subtitle">اختر البرنامج المناسب لك وابدأ رحلة التحول</p>
            
            <div class="programs-grid">
                <div class="program-card">
                    <div class="program-image">
                        <i class="fas fa-running"></i>
                    </div>
                    <div class="program-content">
                        <h3>برنامج اللياقة العامة</h3>
                        <p>برنامج شامل لتحسين اللياقة البدنية العامة وبناء القوة والتحمل</p>
                        <ul class="program-features">
                            <li><i class="fas fa-check"></i> 3 جلسات أسبوعياً</li>
                            <li><i class="fas fa-check"></i> خطة تغذية مخصصة</li>
                            <li><i class="fas fa-check"></i> متابعة يومية</li>
                            <li><i class="fas fa-check"></i> دعم فني 24/7</li>
                        </ul>
                        <div class="program-price">299 ريال/شهر</div>
                        <button class="program-btn">اشترك الآن</button>
                    </div>
                </div>

                <div class="program-card">
                    <div class="program-image">
                        <i class="fas fa-weight-hanging"></i>
                    </div>
                    <div class="program-content">
                        <h3>برنامج بناء العضلات</h3>
                        <p>برنامج متخصص لبناء الكتلة العضلية وزيادة القوة</p>
                        <ul class="program-features">
                            <li><i class="fas fa-check"></i> 4 جلسات أسبوعياً</li>
                            <li><i class="fas fa-check"></i> برنامج مكملات غذائية</li>
                            <li><i class="fas fa-check"></i> قياسات دورية</li>
                            <li><i class="fas fa-check"></i> مدرب شخصي</li>
                        </ul>
                        <div class="program-price">499 ريال/شهر</div>
                        <button class="program-btn">اشترك الآن</button>
                    </div>
                </div>

                <div class="program-card">
                    <div class="program-image">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="program-content">
                        <h3>برنامج إنقاص الوزن</h3>
                        <p>برنامج متكامل لإنقاص الوزن بطريقة صحية ومستدامة</p>
                        <ul class="program-features">
                            <li><i class="fas fa-check"></i> 5 جلسات أسبوعياً</li>
                            <li><i class="fas fa-check"></i> استشارة تغذية</li>
                            <li><i class="fas fa-check"></i> متابعة طبية</li>
                            <li><i class="fas fa-check"></i> جلسات جماعية</li>
                        </ul>
                        <div class="program-price">399 ريال/شهر</div>
                        <button class="program-btn">اشترك الآن</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials" id="testimonials">
        <div class="container">
            <h2 class="section-title">ماذا يقول عملاؤنا</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <p class="testimonial-text">"تغيرت حياتي تماماً بعد الانضمام لـ FitLife. فقدت 15 كيلو في 3 أشهر وأصبحت أكثر نشاطاً وحيوية"</p>
                    <div class="testimonial-author">سارة أحمد</div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <p class="testimonial-text">"المدربون محترفون والبرامج مصممة بعناية. حققت أهدافي في بناء العضلات خلال 6 أشهر"</p>
                    <div class="testimonial-author">محمد علي</div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <p class="testimonial-text">"أفضل استثمار قمت به لصحتي. الدعم والمتابعة المستمرة جعلني ألتزم بالبرنامج"</p>
                    <div class="testimonial-author">فاطمة محمد</div>
                </div>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>FitLife</h3>
                    <p>نحن ملتزمون بمساعدتك في تحقيق أهدافك الصحية واللياقة البدنية من خلال برامج مخصصة ومتابعة احترافية.</p>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📧 <EMAIL></p>
                    <p>📞 +966 50 123 4567</p>
                    <p>📍 الرياض، المملكة العربية السعودية</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <p><a href="#programs">البرامج التدريبية</a></p>
                    <p><a href="#testimonials">آراء العملاء</a></p>
                    <p><a href="#">سياسة الخصوصية</a></p>
                    <p><a href="#">الشروط والأحكام</a></p>
                </div>
            </div>
            
            <div class="social-links">
                <a href="#"><i class="fab fa-facebook-f"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-instagram"></i></a>
                <a href="#"><i class="fab fa-youtube"></i></a>
            </div>
            
            <p>&copy; 2024 FitLife. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe program cards
        document.querySelectorAll('.program-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `all 0.6s ease ${index * 0.2}s`;
            observer.observe(card);
        });

        // Observe testimonial cards
        document.querySelectorAll('.testimonial-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `all 0.6s ease ${index * 0.2}s`;
            observer.observe(card);
        });

        // Program button interactions
        document.querySelectorAll('.program-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-check"></i> تم الاشتراك!';
                this.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                setTimeout(() => {
                    this.innerHTML = 'اشترك الآن';
                    this.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                }, 2000);
            });
        });
    </script>
</body>
</html>
