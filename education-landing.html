<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduMaster - منصة التعليم الإلكتروني المتقدمة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        header.scrolled {
            background: rgba(52, 152, 219, 0.95);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            color: #f39c12;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 50%, #e74c3c 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="books" width="50" height="50" patternUnits="userSpaceOnUse"><rect width="50" height="50" fill="none"/><path d="M10,10 L40,10 L40,40 L10,40 Z" fill="rgba(255,255,255,0.05)" stroke="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23books)"/></svg>');
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translateX(0px); }
            100% { transform: translateX(-100px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            animation: slideInUp 1s ease-out 0.3s both;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.95;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-bottom: 2rem;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            color: #f39c12;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 18px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
            animation: slideInUp 1s ease-out 0.9s both;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(231, 76, 60, 0.4);
        }

        /* Courses Section */
        .courses {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(45deg, #3498db, #e74c3c);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .course-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .course-image {
            height: 200px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
            overflow: hidden;
            background-image: url('https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80');
            background-size: cover;
            background-position: center;
        }

        .course-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(52, 152, 219, 0.8), rgba(41, 128, 185, 0.8));
        }

        .course-image i {
            position: relative;
            z-index: 2;
        }

        .course-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .course-card:hover .course-image::before {
            left: 100%;
        }

        .course-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .course-content {
            padding: 2rem;
        }

        .course-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .course-content p {
            color: #7f8c8d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #95a5a6;
        }

        .course-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .course-price {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .price-current {
            font-size: 1.8rem;
            font-weight: 700;
            color: #27ae60;
        }

        .price-old {
            font-size: 1.2rem;
            color: #95a5a6;
            text-decoration: line-through;
        }

        .course-btn {
            width: 100%;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .course-btn:hover {
            background: linear-gradient(45deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
        }

        /* Features Section */
        .features {
            padding: 100px 0;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .feature-item {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            color: #f39c12;
            margin-bottom: 1rem;
            display: block;
        }

        .feature-item h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
        }

        .feature-item p {
            opacity: 0.9;
            line-height: 1.6;
        }

        /* Footer */
        footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #3498db;
        }

        .footer-section p, .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            line-height: 1.8;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #3498db;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <div class="logo">
                <i class="fas fa-graduation-cap"></i>
                EduMaster
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#courses">الدورات</a></li>
                <li><a href="#features">المميزات</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <h1>تعلم بلا حدود</h1>
                <p>منصة التعليم الإلكتروني الأكثر تطوراً في الشرق الأوسط. اكتسب مهارات جديدة واحصل على شهادات معتمدة من أفضل الخبراء والمؤسسات التعليمية</p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">50,000+</span>
                        <span class="stat-label">طالب نشط</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">دورة تدريبية</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100+</span>
                        <span class="stat-label">مدرب خبير</span>
                    </div>
                </div>

                <a href="#courses" class="cta-button">
                    <i class="fas fa-play"></i>
                    ابدأ التعلم الآن
                </a>
            </div>
        </div>
    </section>

    <section class="courses" id="courses">
        <div class="container">
            <h2 class="section-title">الدورات الأكثر شعبية</h2>
            <p class="section-subtitle">اختر من بين مئات الدورات المتخصصة في مختلف المجالات</p>

            <div class="courses-grid">
                <div class="course-card">
                    <div class="course-image">
                        <i class="fas fa-code"></i>
                        <div class="course-badge">الأكثر مبيعاً</div>
                    </div>
                    <div class="course-content">
                        <h3>تطوير المواقع الإلكترونية</h3>
                        <p>تعلم تطوير المواقع من الصفر باستخدام HTML, CSS, JavaScript وReact</p>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> 40 ساعة</span>
                            <span><i class="fas fa-users"></i> 1,250 طالب</span>
                            <span><i class="fas fa-star"></i> 4.8</span>
                        </div>
                        <div class="course-price">
                            <span class="price-current">299 ريال</span>
                            <span class="price-old">599 ريال</span>
                        </div>
                        <button class="course-btn">
                            <i class="fas fa-shopping-cart"></i>
                            اشترك الآن
                        </button>
                    </div>
                </div>

                <div class="course-card">
                    <div class="course-image">
                        <i class="fas fa-chart-line"></i>
                        <div class="course-badge">جديد</div>
                    </div>
                    <div class="course-content">
                        <h3>التسويق الرقمي</h3>
                        <p>احترف التسويق الإلكتروني ووسائل التواصل الاجتماعي وإعلانات جوجل</p>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> 35 ساعة</span>
                            <span><i class="fas fa-users"></i> 890 طالب</span>
                            <span><i class="fas fa-star"></i> 4.9</span>
                        </div>
                        <div class="course-price">
                            <span class="price-current">399 ريال</span>
                            <span class="price-old">699 ريال</span>
                        </div>
                        <button class="course-btn">
                            <i class="fas fa-shopping-cart"></i>
                            اشترك الآن
                        </button>
                    </div>
                </div>

                <div class="course-card">
                    <div class="course-image">
                        <i class="fas fa-palette"></i>
                        <div class="course-badge">محدود</div>
                    </div>
                    <div class="course-content">
                        <h3>التصميم الجرافيكي</h3>
                        <p>تعلم أساسيات وتقنيات التصميم باستخدام Photoshop وIllustrator</p>
                        <div class="course-meta">
                            <span><i class="fas fa-clock"></i> 30 ساعة</span>
                            <span><i class="fas fa-users"></i> 650 طالب</span>
                            <span><i class="fas fa-star"></i> 4.7</span>
                        </div>
                        <div class="course-price">
                            <span class="price-current">249 ريال</span>
                            <span class="price-old">449 ريال</span>
                        </div>
                        <button class="course-btn">
                            <i class="fas fa-shopping-cart"></i>
                            اشترك الآن
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2 class="section-title">لماذا تختار EduMaster؟</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-certificate feature-icon"></i>
                    <h3>شهادات معتمدة</h3>
                    <p>احصل على شهادات معتمدة دولياً تعزز من فرصك المهنية</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-users feature-icon"></i>
                    <h3>مدربون خبراء</h3>
                    <p>تعلم من أفضل الخبراء والمتخصصين في مختلف المجالات</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt feature-icon"></i>
                    <h3>تعلم في أي مكان</h3>
                    <p>منصة متوافقة مع جميع الأجهزة للتعلم في أي وقت ومكان</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-headset feature-icon"></i>
                    <h3>دعم فني 24/7</h3>
                    <p>فريق دعم متاح على مدار الساعة لمساعدتك في رحلتك التعليمية</p>
                </div>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>EduMaster</h3>
                    <p>منصة التعليم الإلكتروني الرائدة في الشرق الأوسط. نحن ملتزمون بتوفير تعليم عالي الجودة ومتاح للجميع.</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <p><a href="#courses">الدورات التدريبية</a></p>
                    <p><a href="#">المدربون</a></p>
                    <p><a href="#">الشهادات</a></p>
                    <p><a href="#">المدونة</a></p>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📧 <EMAIL></p>
                    <p>📞 +966 50 123 4567</p>
                    <p>📍 الرياض، المملكة العربية السعودية</p>
                </div>
                <div class="footer-section">
                    <h3>الدعم</h3>
                    <p><a href="#">مركز المساعدة</a></p>
                    <p><a href="#">الأسئلة الشائعة</a></p>
                    <p><a href="#">سياسة الخصوصية</a></p>
                    <p><a href="#">الشروط والأحكام</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 EduMaster. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe course cards
        document.querySelectorAll('.course-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `all 0.6s ease ${index * 0.2}s`;
            observer.observe(card);
        });

        // Observe feature items
        document.querySelectorAll('.feature-item').forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(50px)';
            item.style.transition = `all 0.6s ease ${index * 0.1}s`;
            observer.observe(item);
        });

        // Course button interactions
        document.querySelectorAll('.course-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-check"></i> تم التسجيل!';
                this.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-shopping-cart"></i> اشترك الآن';
                    this.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
                }, 2000);
            });
        });

        // Animate stats counter
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString() + '+';
            }, 20);
        }

        // Trigger stats animation on scroll
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = document.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const text = stat.textContent;
                        const number = parseInt(text.replace(/[^0-9]/g, ''));
                        animateCounter(stat, number);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsObserver.observe(document.querySelector('.hero-stats'));
    </script>
</body>
</html>
