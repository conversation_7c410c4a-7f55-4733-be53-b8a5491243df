<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ConsultPro - خدمات استشارية متخصصة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        header.scrolled {
            background: rgba(44, 62, 80, 0.95);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo i {
            color: #f39c12;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 8px 16px;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.9)),
                        url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="consulting" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="3" fill="rgba(255,255,255,0.05)"/><path d="M15,15 L35,15 L35,35 L15,35 Z" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23consulting)"/></svg>');
            animation: drift 30s linear infinite;
        }

        @keyframes drift {
            0% { transform: translateX(0px) translateY(0px); }
            25% { transform: translateX(-10px) translateY(-5px); }
            50% { transform: translateX(10px) translateY(-10px); }
            75% { transform: translateX(-5px) translateY(5px); }
            100% { transform: translateX(0px) translateY(0px); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            animation: slideInDown 1s ease-out;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            animation: slideInUp 1s ease-out 0.3s both;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.95;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-bottom: 2rem;
            animation: fadeIn 1s ease-out 0.6s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            display: block;
            color: #f39c12;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 18px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(243, 156, 18, 0.3);
            animation: slideInUp 1s ease-out 0.9s both;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(243, 156, 18, 0.4);
        }

        /* Services Section */
        .services {
            padding: 100px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(45deg, #2c3e50, #f39c12);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .service-header {
            height: 200px;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
            overflow: hidden;
            background-size: cover;
            background-position: center;
        }

        .service-header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(44, 62, 80, 0.8), rgba(52, 73, 94, 0.8));
        }

        .service-header i {
            position: relative;
            z-index: 2;
            color: #f39c12;
        }

        .service-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
            z-index: 3;
        }

        .service-card:hover .service-header::before {
            left: 100%;
        }

        .service-content {
            padding: 2rem;
        }

        .service-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .service-content p {
            color: #7f8c8d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .service-features {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .service-features li {
            padding: 5px 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .service-features li i {
            color: #f39c12;
        }

        .service-price {
            font-size: 1.8rem;
            font-weight: 700;
            color: #27ae60;
            text-align: center;
            margin-bottom: 1rem;
        }

        .service-btn {
            width: 100%;
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 12px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .service-btn:hover {
            background: linear-gradient(45deg, #1a252f, #2c3e50);
            transform: translateY(-2px);
        }

        /* Team Section */
        .team {
            padding: 100px 0;
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .team-member {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .team-member:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.15);
        }

        .member-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            background-size: cover;
            background-position: center;
        }

        .member-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .member-title {
            color: #f39c12;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .member-bio {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .member-social {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .member-social a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            color: white;
            border-radius: 50%;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .member-social a:hover {
            background: #f39c12;
            transform: translateY(-3px);
        }

        /* Footer */
        footer {
            background: #2c3e50;
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h3 {
            margin-bottom: 1rem;
            color: #f39c12;
        }

        .footer-section p, .footer-section a {
            color: #bdc3c7;
            text-decoration: none;
            line-height: 1.8;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: #f39c12;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid #34495e;
            color: #95a5a6;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header id="header">
        <nav class="container">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                ConsultPro
            </div>
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#services">الخدمات</a></li>
                <li><a href="#team">الفريق</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <h1>نحو نجاح مستدام</h1>
                <p>خدمات استشارية متخصصة لتطوير أعمالك وتحقيق أهدافك الاستراتيجية مع فريق من الخبراء المعتمدين في مختلف المجالات</p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">عميل راضٍ</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">سنة خبرة</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">98%</span>
                        <span class="stat-label">معدل النجاح</span>
                    </div>
                </div>

                <a href="#services" class="cta-button">
                    <i class="fas fa-calendar-check"></i>
                    احجز استشارة مجانية
                </a>
            </div>
        </div>
    </section>

    <section class="services" id="services">
        <div class="container">
            <h2 class="section-title">خدماتنا الاستشارية</h2>
            <p class="section-subtitle">نقدم حلولاً استشارية شاملة ومخصصة لتلبية احتياجات عملك</p>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="service-content">
                        <h3>الاستشارات الإدارية</h3>
                        <p>تطوير الهياكل التنظيمية وتحسين العمليات الإدارية لزيادة الكفاءة والإنتاجية</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> تحليل الهيكل التنظيمي</li>
                            <li><i class="fas fa-check"></i> تطوير السياسات والإجراءات</li>
                            <li><i class="fas fa-check"></i> تحسين الأداء المؤسسي</li>
                            <li><i class="fas fa-check"></i> إدارة التغيير</li>
                        </ul>
                        <div class="service-price">ابتداءً من 5,000 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-bullseye"></i>
                    </div>
                    <div class="service-content">
                        <h3>التخطيط الاستراتيجي</h3>
                        <p>وضع خطط استراتيجية شاملة لتحقيق الأهداف طويلة المدى وضمان النمو المستدام</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> تحليل البيئة التنافسية</li>
                            <li><i class="fas fa-check"></i> وضع الرؤية والرسالة</li>
                            <li><i class="fas fa-check"></i> تحديد الأهداف الاستراتيجية</li>
                            <li><i class="fas fa-check"></i> خطط التنفيذ والمتابعة</li>
                        </ul>
                        <div class="service-price">ابتداءً من 8,000 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="service-content">
                        <h3>الاستشارات المالية</h3>
                        <p>تحليل الوضع المالي وتطوير استراتيجيات مالية فعالة لتحسين الربحية</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> تحليل القوائم المالية</li>
                            <li><i class="fas fa-check"></i> إعداد الميزانيات التقديرية</li>
                            <li><i class="fas fa-check"></i> تحليل التكاليف والربحية</li>
                            <li><i class="fas fa-check"></i> استراتيجيات التمويل</li>
                        </ul>
                        <div class="service-price">ابتداءً من 6,000 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="service-content">
                        <h3>تطوير الموارد البشرية</h3>
                        <p>بناء استراتيجيات شاملة لإدارة وتطوير الموارد البشرية وتحسين بيئة العمل</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> تصميم الهياكل الوظيفية</li>
                            <li><i class="fas fa-check"></i> برامج التدريب والتطوير</li>
                            <li><i class="fas fa-check"></i> أنظمة الأداء والحوافز</li>
                            <li><i class="fas fa-check"></i> استراتيجيات الاستقطاب</li>
                        </ul>
                        <div class="service-price">ابتداءً من 4,500 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-megaphone"></i>
                    </div>
                    <div class="service-content">
                        <h3>استشارات التسويق</h3>
                        <p>تطوير استراتيجيات تسويقية مبتكرة لزيادة الوعي بالعلامة التجارية والمبيعات</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> دراسة السوق والمنافسين</li>
                            <li><i class="fas fa-check"></i> استراتيجيات التسويق الرقمي</li>
                            <li><i class="fas fa-check"></i> تطوير العلامة التجارية</li>
                            <li><i class="fas fa-check"></i> حملات إعلانية متكاملة</li>
                        </ul>
                        <div class="service-price">ابتداءً من 7,000 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header" style="background-image: url('https://images.unsplash.com/photo-1518186285589-2f7649de83e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <div class="service-content">
                        <h3>التحول الرقمي</h3>
                        <p>مساعدة الشركات في رحلة التحول الرقمي وتطبيق أحدث التقنيات</p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> تقييم الوضع التقني الحالي</li>
                            <li><i class="fas fa-check"></i> استراتيجية التحول الرقمي</li>
                            <li><i class="fas fa-check"></i> اختيار الحلول التقنية</li>
                            <li><i class="fas fa-check"></i> إدارة مشاريع التحول</li>
                        </ul>
                        <div class="service-price">ابتداءً من 10,000 ريال</div>
                        <button class="service-btn">احجز استشارة</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="team" id="team">
        <div class="container">
            <h2 class="section-title">فريق الخبراء</h2>
            <div class="team-grid">
                <div class="team-member">
                    <div class="member-photo" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80');">
                    </div>
                    <h3 class="member-name">أحمد محمد</h3>
                    <p class="member-title">مستشار إداري أول</p>
                    <p class="member-bio">خبرة 15 عاماً في الاستشارات الإدارية وتطوير الأعمال مع شركات رائدة في المنطقة</p>
                    <div class="member-social">
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <div class="member-photo" style="background-image: url('https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80');">
                    </div>
                    <h3 class="member-name">فاطمة العلي</h3>
                    <p class="member-title">خبيرة التخطيط الاستراتيجي</p>
                    <p class="member-bio">متخصصة في التخطيط الاستراتيجي مع ماجستير في إدارة الأعمال من جامعة هارفارد</p>
                    <div class="member-social">
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <div class="member-photo" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80');">
                    </div>
                    <h3 class="member-name">خالد السعد</h3>
                    <p class="member-title">مستشار مالي معتمد</p>
                    <p class="member-bio">محاسب قانوني معتمد مع خبرة 12 عاماً في الاستشارات المالية والاستثمارية</p>
                    <div class="member-social">
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>

                <div class="team-member">
                    <div class="member-photo" style="background-image: url('https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80');">
                    </div>
                    <h3 class="member-name">نورا أحمد</h3>
                    <p class="member-title">خبيرة الموارد البشرية</p>
                    <p class="member-bio">متخصصة في تطوير الموارد البشرية مع شهادات دولية في إدارة المواهب والقيادة</p>
                    <div class="member-social">
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ConsultPro</h3>
                    <p>شريكك الاستراتيجي في النجاح. نقدم حلولاً استشارية مبتكرة ومخصصة لتحقيق أهدافك وتطوير أعمالك.</p>
                </div>
                <div class="footer-section">
                    <h3>خدماتنا</h3>
                    <p><a href="#services">الاستشارات الإدارية</a></p>
                    <p><a href="#services">التخطيط الاستراتيجي</a></p>
                    <p><a href="#services">الاستشارات المالية</a></p>
                    <p><a href="#services">تطوير الموارد البشرية</a></p>
                </div>
                <div class="footer-section">
                    <h3>تواصل معنا</h3>
                    <p>📧 <EMAIL></p>
                    <p>📞 +966 50 123 4567</p>
                    <p>📍 الرياض، المملكة العربية السعودية</p>
                    <p>🕒 الأحد - الخميس: 9:00 - 17:00</p>
                </div>
                <div class="footer-section">
                    <h3>روابط مفيدة</h3>
                    <p><a href="#">حول الشركة</a></p>
                    <p><a href="#">دراسات الحالة</a></p>
                    <p><a href="#">المدونة</a></p>
                    <p><a href="#">سياسة الخصوصية</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 ConsultPro. MOSTAFA جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate elements on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe service cards
        document.querySelectorAll('.service-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = `all 0.6s ease ${index * 0.1}s`;
            observer.observe(card);
        });

        // Observe team members
        document.querySelectorAll('.team-member').forEach((member, index) => {
            member.style.opacity = '0';
            member.style.transform = 'translateY(50px)';
            member.style.transition = `all 0.6s ease ${index * 0.2}s`;
            observer.observe(member);
        });

        // Service button interactions
        document.querySelectorAll('.service-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-check"></i> تم الحجز!';
                this.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                setTimeout(() => {
                    this.innerHTML = 'احجز استشارة';
                    this.style.background = 'linear-gradient(45deg, #2c3e50, #34495e)';
                }, 2000);
            });
        });

        // Animate stats counter
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                if (element.textContent.includes('%')) {
                    element.textContent = Math.floor(current) + '%';
                } else {
                    element.textContent = Math.floor(current) + '+';
                }
            }, 20);
        }

        // Trigger stats animation on scroll
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const statNumbers = document.querySelectorAll('.stat-number');
                    statNumbers.forEach(stat => {
                        const text = stat.textContent;
                        const number = parseInt(text.replace(/[^0-9]/g, ''));
                        animateCounter(stat, number);
                    });
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsObserver.observe(document.querySelector('.hero-stats'));
    </script>
</body>
</html>
