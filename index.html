<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معرض صفحات الهبوط الاحترافية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeInDown 1s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .portfolio-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.2);
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .portfolio-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        }

        .card-image {
            height: 200px;
            background-size: cover;
            background-position: center;
            position: relative;
            overflow: hidden;
        }

        .card-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
        }

        .card-image::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s ease;
        }

        .portfolio-card:hover .card-image::after {
            left: 100%;
        }

        .card-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 4rem;
            color: white;
            z-index: 2;
        }

        .card-content {
            padding: 2rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }

        .card-description {
            color: rgba(255,255,255,0.9);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .card-features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .card-features li {
            padding: 5px 0;
            color: rgba(255,255,255,0.8);
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        .card-features li i {
            color: #f39c12;
        }

        .card-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn-view {
            flex: 1;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-view:hover {
            background: linear-gradient(45deg, #e67e22, #d35400);
            transform: translateY(-2px);
        }

        .btn-code {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 15px;
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-code:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .stats {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
            animation: fadeIn 1s ease-out 0.5s both;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .stats h2 {
            font-size: 2rem;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #f39c12;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(255,255,255,0.2);
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .portfolio-grid {
                grid-template-columns: 1fr;
            }
            
            .card-buttons {
                flex-direction: column;
            }
        }

        /* Animation delays for cards */
        .portfolio-card:nth-child(1) { animation-delay: 0.1s; }
        .portfolio-card:nth-child(2) { animation-delay: 0.2s; }
        .portfolio-card:nth-child(3) { animation-delay: 0.3s; }
        .portfolio-card:nth-child(4) { animation-delay: 0.4s; }
        .portfolio-card:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> معرض صفحات الهبوط الاحترافية</h1>
            <p>مجموعة من صفحات الهبوط المتقدمة والجذابة المصممة لجذب الزوار وتحويلهم إلى عملاء</p>
        </div>

        <div class="portfolio-grid">
            <div class="portfolio-card">
                <div class="card-image" style="background-image: url('https://images.unsplash.com/photo-1563013544-824ae1b704d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="card-title">FinPay - التكنولوجيا المالية</h3>
                    <p class="card-description">صفحة هبوط متطورة لشركة تقنية مالية مع تصميم مودرن وتأثيرات بصرية متقدمة</p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> تصميم متجاوب ومتوافق مع جميع الأجهزة</li>
                        <li><i class="fas fa-check"></i> رسوم متحركة CSS متقدمة</li>
                        <li><i class="fas fa-check"></i> إحصائيات متحركة وتفاعلية</li>
                        <li><i class="fas fa-check"></i> تدرجات لونية جذابة</li>
                    </ul>
                    <div class="card-buttons">
                        <a href="fintech-landing.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </a>
                        <a href="#" class="btn-code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="portfolio-card">
                <div class="card-image" style="background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                    <div class="card-icon">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="card-title">FitLife - الصحة واللياقة</h3>
                    <p class="card-description">صفحة هبوط ديناميكية لنادي رياضي مع ألوان منعشة وعناصر تفاعلية</p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> تصميم بألوان خضراء منعشة</li>
                        <li><i class="fas fa-check"></i> بطاقات برامج تدريبية تفاعلية</li>
                        <li><i class="fas fa-check"></i> قسم شهادات العملاء</li>
                        <li><i class="fas fa-check"></i> تأثيرات حركية سلسة</li>
                    </ul>
                    <div class="card-buttons">
                        <a href="fitness-landing.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </a>
                        <a href="#" class="btn-code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="portfolio-card">
                <div class="card-image" style="background-image: url('https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                    <div class="card-icon">
                        <i class="fas fa-graduation-cap"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="card-title">EduMaster - التعليم الإلكتروني</h3>
                    <p class="card-description">منصة تعليمية متقدمة بتصميم أكاديمي أنيق وعرض تفاعلي للدورات</p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> تصميم أكاديمي احترافي</li>
                        <li><i class="fas fa-check"></i> بطاقات دورات تفاعلية</li>
                        <li><i class="fas fa-check"></i> إحصائيات متحركة للطلاب</li>
                        <li><i class="fas fa-check"></i> قسم مميزات المنصة</li>
                    </ul>
                    <div class="card-buttons">
                        <a href="education-landing.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </a>
                        <a href="#" class="btn-code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="portfolio-card">
                <div class="card-image" style="background-image: url('https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                    <div class="card-icon">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="card-title">ShopZone - التجارة الإلكترونية</h3>
                    <p class="card-description">متجر إلكتروني عصري مع عرض منتجات تفاعلي ونظام سلة تسوق</p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> عرض منتجات تفاعلي</li>
                        <li><i class="fas fa-check"></i> نظام تقييمات وتفضيلات</li>
                        <li><i class="fas fa-check"></i> فئات منتجات منظمة</li>
                        <li><i class="fas fa-check"></i> سلة تسوق ديناميكية</li>
                    </ul>
                    <div class="card-buttons">
                        <a href="ecommerce-landing.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </a>
                        <a href="#" class="btn-code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="portfolio-card">
                <div class="card-image" style="background-image: url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');">
                    <div class="card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="card-content">
                    <h3 class="card-title">ConsultPro - الخدمات الاستشارية</h3>
                    <p class="card-description">صفحة استشارية مهنية مع عرض الخبرات وفريق العمل بتصميم أنيق</p>
                    <ul class="card-features">
                        <li><i class="fas fa-check"></i> تصميم مهني وأنيق</li>
                        <li><i class="fas fa-check"></i> عرض خدمات استشارية متنوعة</li>
                        <li><i class="fas fa-check"></i> قسم فريق الخبراء</li>
                        <li><i class="fas fa-check"></i> نموذج حجز استشارة</li>
                    </ul>
                    <div class="card-buttons">
                        <a href="consulting-landing.html" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </a>
                        <a href="#" class="btn-code">
                            <i class="fas fa-code"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats">
            <h2><i class="fas fa-chart-bar"></i> إحصائيات المعرض</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">صفحات هبوط</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">متجاوبة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <span class="stat-label">مجالات مختلفة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">CSS3</span>
                    <span class="stat-label">تقنيات حديثة</span>
                </div>
            </div>
        </div>

        <div class="footer">
            <p><i class="fas fa-heart" style="color: #e74c3c;"></i> تم التصميم بعناية لمعرض أعمال احترافي</p>
            <p>&copy; 2025 - MOSTAFA جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        // Add smooth animations on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all portfolio cards
        document.querySelectorAll('.portfolio-card').forEach(card => {
            observer.observe(card);
        });

        // Add click animation to buttons
        document.querySelectorAll('.btn-view, .btn-code').forEach(btn => {
            btn.addEventListener('click', function(e) {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-2px)';
                }, 150);
            });
        });

        // Add hover effect to cards
        document.querySelectorAll('.portfolio-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
