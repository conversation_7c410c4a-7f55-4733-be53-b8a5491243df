<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصميم صفحة هبوط تسويقية احترافية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .service-card {
            width: 720px;
            height: 400px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            transition: all 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
        }

        .left-section {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .left-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s linear infinite;
        }

        @keyframes float {
            0% { transform: translateX(0px) translateY(0px); }
            50% { transform: translateX(-20px) translateY(-10px); }
            100% { transform: translateX(0px) translateY(0px); }
        }

        .service-title {
            position: relative;
            z-index: 2;
        }

        .service-title h1 {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 15px;
            line-height: 1.3;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .service-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 25px;
            line-height: 1.5;
            position: relative;
            z-index: 2;
        }

        .features-list {
            list-style: none;
            position: relative;
            z-index: 2;
        }

        .features-list li {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
        }

        .features-list li i {
            background: rgba(255,255,255,0.2);
            color: #fbbf24;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 12px;
            font-size: 12px;
        }

        .price-section {
            position: relative;
            z-index: 2;
            margin-top: 20px;
        }

        .price {
            font-size: 24px;
            font-weight: 800;
            color: #fbbf24;
            margin-bottom: 8px;
        }

        .price-note {
            font-size: 12px;
            opacity: 0.8;
        }

        .right-section {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            position: relative;
        }

        .laptop-mockup {
            width: 280px;
            height: 180px;
            background: #1f2937;
            border-radius: 12px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transform: perspective(1000px) rotateY(-15deg) rotateX(5deg);
            transition: all 0.3s ease;
        }

        .service-card:hover .laptop-mockup {
            transform: perspective(1000px) rotateY(-10deg) rotateX(2deg);
        }

        .laptop-screen {
            width: 260px;
            height: 160px;
            background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            border-radius: 8px;
            margin: 10px auto;
            position: relative;
            overflow: hidden;
        }

        .laptop-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 120"><rect x="10" y="10" width="180" height="8" fill="rgba(255,255,255,0.3)" rx="4"/><rect x="10" y="25" width="120" height="6" fill="rgba(255,255,255,0.2)" rx="3"/><rect x="10" y="35" width="160" height="6" fill="rgba(255,255,255,0.2)" rx="3"/><rect x="10" y="50" width="80" height="20" fill="rgba(255,255,255,0.4)" rx="10"/><rect x="100" y="50" width="80" height="20" fill="rgba(255,255,255,0.3)" rx="10"/><circle cx="30" cy="90" r="8" fill="rgba(255,255,255,0.3)"/><circle cx="60" cy="90" r="8" fill="rgba(255,255,255,0.3)"/><circle cx="90" cy="90" r="8" fill="rgba(255,255,255,0.3)"/></svg>');
        }

        .laptop-base {
            width: 300px;
            height: 8px;
            background: #374151;
            border-radius: 0 0 20px 20px;
            margin-top: -4px;
        }

        .icons-container {
            display: flex;
            justify-content: space-around;
            margin-top: 25px;
            width: 100%;
        }

        .service-icon {
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .service-icon:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .service-icon i {
            font-size: 20px;
            color: #3b82f6;
        }

        .service-icon::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: #1f2937;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 10px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .service-icon:hover::after {
            opacity: 1;
        }

        .quality-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(45deg, #10b981, #059669);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .rating-stars {
            position: absolute;
            bottom: 15px;
            right: 15px;
            display: flex;
            gap: 2px;
        }

        .rating-stars i {
            color: #fbbf24;
            font-size: 12px;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .floating-element {
            position: absolute;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
            animation: floatUp 6s infinite ease-in-out;
        }

        .floating-element:nth-child(1) {
            width: 20px;
            height: 20px;
            top: 80%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-element:nth-child(2) {
            width: 15px;
            height: 15px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .floating-element:nth-child(3) {
            width: 25px;
            height: 25px;
            top: 40%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes floatUp {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) scale(1.1);
                opacity: 1;
            }
        }

        .tech-pattern {
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tech" width="10" height="10" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23tech)"/></svg>');
            opacity: 0.5;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .service-card {
                width: 100%;
                max-width: 500px;
                height: auto;
                flex-direction: column;
            }
            
            .left-section {
                padding: 30px;
            }
            
            .service-title h1 {
                font-size: 24px;
            }
            
            .laptop-mockup {
                width: 240px;
                height: 150px;
            }
            
            .laptop-screen {
                width: 220px;
                height: 130px;
            }
        }
    </style>
</head>
<body>
    <div class="service-card">
        <div class="quality-badge">
            <i class="fas fa-crown"></i> خدمة مميزة
        </div>
        
        <div class="rating-stars">
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
            <i class="fas fa-star"></i>
        </div>

        <div class="left-section">
            <div class="service-title">
                <h1>تصميم صفحة هبوط تسويقية احترافية</h1>
                <p class="service-subtitle">صفحات هبوط عالية التحويل مصممة خصيصاً لزيادة مبيعاتك وتحويل الزوار إلى عملاء</p>
            </div>

            <ul class="features-list">
                <li>
                    <i class="fas fa-rocket"></i>
                    تصميم احترافي يزيد التحويلات
                </li>
                <li>
                    <i class="fas fa-mobile-alt"></i>
                    متوافق مع جميع الأجهزة
                </li>
                <li>
                    <i class="fas fa-chart-line"></i>
                    زيادة المبيعات بنسبة تصل إلى 300%
                </li>
                <li>
                    <i class="fas fa-users"></i>
                    تحويل الزوار إلى عملاء فعليين
                </li>
                <li>
                    <i class="fas fa-clock"></i>
                    تسليم خلال 3-5 أيام عمل
                </li>
            </ul>

            <div class="price-section">
                <div class="price">ابتداءً من 299 ريال</div>
                <div class="price-note">شامل التصميم والبرمجة والاستضافة</div>
            </div>
        </div>

        <div class="right-section">
            <div class="tech-pattern"></div>
            
            <div class="laptop-mockup">
                <div class="laptop-screen"></div>
                <div class="laptop-base"></div>
            </div>

            <div class="icons-container">
                <div class="service-icon" data-tooltip="تسويق رقمي">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <div class="service-icon" data-tooltip="تصميم إبداعي">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="service-icon" data-tooltip="تحويل الزوار">
                    <i class="fas fa-exchange-alt"></i>
                </div>
            </div>

            <div class="floating-elements">
                <div class="floating-element"></div>
                <div class="floating-element"></div>
                <div class="floating-element"></div>
            </div>
        </div>
    </div>

    <script>
        // Add interactive hover effects
        const serviceCard = document.querySelector('.service-card');
        const laptopMockup = document.querySelector('.laptop-mockup');
        
        serviceCard.addEventListener('mousemove', (e) => {
            const rect = serviceCard.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;
            
            laptopMockup.style.transform = `perspective(1000px) rotateY(${-15 + rotateY}deg) rotateX(${5 + rotateX}deg)`;
        });
        
        serviceCard.addEventListener('mouseleave', () => {
            laptopMockup.style.transform = 'perspective(1000px) rotateY(-15deg) rotateX(5deg)';
        });

        // Animate floating elements
        const floatingElements = document.querySelectorAll('.floating-element');
        floatingElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 2}s`;
        });

        // Add click animation to service icons
        const serviceIcons = document.querySelectorAll('.service-icon');
        serviceIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                this.style.transform = 'translateY(-3px) scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-3px) scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
